import request from '@/utils/request'

// 查询设备数据列表
export function listBaseEquipment(query) {
  return request({
    url: '/base/baseEquipment/list',
    method: 'get',
    params: query
  })
}

// 查询设备数据详细
export function getBaseEquipment(id) {
  return request({
    url: '/base/baseEquipment/' + id,
    method: 'get'
  })
}

// 新增设备数据
export function addBaseEquipment(data) {
  return request({
    url: '/base/baseEquipment',
    method: 'post',
    data: data
  })
}

// 修改设备数据
export function updateBaseEquipment(data) {
  return request({
    url: '/base/baseEquipment',
    method: 'put',
    data: data
  })
}

// 删除设备数据
export function delBaseEquipment(id) {
  return request({
    url: '/base/baseEquipment/' + id,
    method: 'delete'
  })
}
