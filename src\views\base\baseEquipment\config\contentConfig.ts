export const tableItem: BaseTableItem[] = [
  {
    prop: 'id',
    label: '设备数据ID',
    width: '80',
  },

  {
    prop: 'equipmentNo',
    label: '设备编号'
  },
  {
    prop: 'mineCarsNumber',
    label: '矿车数量'
  },
  {
    prop: 'unitEfficiency',
    label: '单位效率'
  },
  {
    prop: 'unit',
    label: '单位'
  },
  {
    prop: 'status',
    label: '状态',
    width: '100',
    slotName: 'status',
    isDict: true
  },

  {
    prop: 'startTime',
    label: '开始时间'
  },
  {
    prop: 'endTime',
    label: '结束时间'
  },
  {
    prop: 'todo',
    label: '操作',
    width: '200',
    fixed: !window.isSmallScreen ? 'right' : false,
    slotName: 'todo',
    showOverflowTooltip: false,
  },

]
export default (): BaseTableProps => {
  return {
    tableItem,
    elTableConfig: {
      tooltipOptions: {
        popperClass: 'lmw_popper',
        effect: 'light',
      },
      rowKey: 'id',
    },
    showIndex: false,
    showChoose: true,
    pagination: true,
  }
}
