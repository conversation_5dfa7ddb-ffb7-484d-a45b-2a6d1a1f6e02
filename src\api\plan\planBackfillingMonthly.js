import request from '@/utils/request'

// 查询充填月计划列表
export function listPlanBackfillingMonthly(query) {
  return request({
    url: '/plan/planBackfillingMonthly/list',
    method: 'get',
    params: query
  })
}

// 查询充填月计划详细
export function getPlanBackfillingMonthly(id) {
  return request({
    url: '/plan/planBackfillingMonthly/' + id,
    method: 'get'
  })
}

// 新增充填月计划
export function addPlanBackfillingMonthly(data) {
  return request({
    url: '/plan/planBackfillingMonthly',
    method: 'post',
    data: data
  })
}

// 修改充填月计划
export function updatePlanBackfillingMonthly(data) {
  return request({
    url: '/plan/planBackfillingMonthly',
    method: 'put',
    data: data
  })
}

// 删除充填月计划
export function delPlanBackfillingMonthly(id) {
  return request({
    url: '/plan/planBackfillingMonthly/' + id,
    method: 'delete'
  })
}
