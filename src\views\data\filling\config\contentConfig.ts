export const tableItem: BaseTableItem[] = [
  {
    prop: 'operationDate',
    label: '作业日期',
  },
  {
    prop: 'projectDepartmentName',
    label: '项目部门',
  },
  {
    prop: 'stopeName',
    label: '采场',
  },
  {
    prop: 'workingPeriodName',
    label: '作业时段',
  },
  {
    prop: 'slurryVolume',
    label: '生产料浆量(m³)',
  },
  {
    prop: 'cementWeight',
    label: '胶固粉量(t)',
  },
  {
    prop: 'fillingRatio',
    label: '充填比例',
  },
  {
    prop: 'fillingConcentration',
    label: '充填浓度(%)',
  },
  {
    prop: 'firstFillingTime',
    label: '首冲时间',
  },
  {
    prop: 'endFillingTime',
    label: '结束时间',
  },
  {
    prop: 'remarks',
    label: '备注',
  },
  {
    prop: 'todo',
    label: '操作',
    width: '200',
    fixed: !window.isSmallScreen ? 'right' : false,
    slotName: 'todo',
    showOverflowTooltip: false,
  },
]
export default (): BaseTableProps => {
  return {
    tableItem,
    elTableConfig: {
      tooltipOptions: {
        popperClass: 'lmw_popper',
        effect: 'light',
      },
      rowKey: 'id',
    },
    showIndex: false,
    showChoose: true,
    pagination: true,
  }
}
