export const tableItem: BaseTableItem[] = [
  {
    prop: 'id',
    label: '采场ID',
    width: '80',
  },

  {
    prop: 'stopeName',
    label: '重点工程'
  },
  {
    prop: 'workingFaceName',
    label: '工作面'
  },
  {
    prop: 'status',
    label: '状态',
    width: '100',
    slotName: 'status',
    isDict: true
  },

  {
    prop: 'startTime',
    label: '采场开始时间'
  },
  {
    prop: 'endTime',
    label: '采场结束时间'
  },
  {
    prop: 'todo',
    label: '操作',
    width: '200',
    fixed: !window.isSmallScreen ? 'right' : false,
    slotName: 'todo',
    showOverflowTooltip: false,
  },

]
export default (): BaseTableProps => {
  return {
    tableItem,
    elTableConfig: {
      tooltipOptions: {
        popperClass: 'lmw_popper',
        effect: 'light',
      },
      rowKey: 'id',
    },
    showIndex: false,
    showChoose: true,
    pagination: true,
  }
}
