export default ():BaseFormProps => {
  return {
    itemStyle: {
      padding: '0px 0px 0px 0px',
    },
    rules: {
    },
    formItems: [
    {
        field: 'stopeId',
        type: 'select',
        options: ref([]),
        label: '采场Id',
      },
    {
        field: 'workingFaceId',
        type: 'select',
        options: ref([]),
        label: '工作面ID',
      },
    {
        field: 'status',
        type: 'select',
        options: [],
        label: '状态',
      },
    {
      field: 'startTime',
      label: '采场开始时间',
      config: { clearable: false, type: 'date' },
      type: 'datepicker',
    },
    {
      field: 'endTime',
      label: '采场结束时间',
      config: { clearable: false, type: 'date' },
      type: 'datepicker',
    }
    ],
    colLayout: {
      xl: 24,
      lg: 24,
      md: 24,
      sm: 24,
      xs: 24,
    },
  }
}
