// 自动生成的 API 文件，请勿手动修改

import { request, blob } from '@/utils/hsj/service/index'
import { AxiosResponse } from 'axios'
import { TDataTunneling, TDataTunnelingBatchDto, TDataMuckingOut, TDataMuckingOutBatchDto, TDataOrepassOperation, TDataOrepassOperationBatchDto, TDataMineHoisting, TDataMineHoistingBatchDto, TDataFilling, TDataFillingBatchDto, TDataDrilling, TDataDrillingBatchDto, TDataEquipment, TDataCrushingOperation, TDataCrushingOperationBatchDto, TDataTunnelingDepartmentWithPlanStats, TDataTunnelingTotalWithPlanStats, TDataSupportDepartmentWithPlanStats, TDataSupportTotalWithPlanStats, TDataSupportTypeDepartmentWithPlanStats, TDataSupportTypeTotalWithPlanStats, TDataMuckingOutTotalWithPlanStats, TDataMuckingOutPeriodStats, TDataMuckingOutDepartmentStats, TDataMuckingOutStopeStats, TDataMuckingOutTotalStats, TDataOrepassOperationOrepassStats, TDataOrepassOperationPeriodStats, TDataOrepassOperationDepartmentStats, TDataOrepassOperationStats, TDataMineHoistingPeriodStats, TDataMineHoistingStats, TDataFillingStopeStats, TDataFillingTotalWithPlanStats, TDataDrillingDepartmentWithPlanStats, TDataDrillingTotalWithPlanStats, TDataDrillingTypeStats, TDataDrillingWorkingFaceStats, TDataDrillingPeriodStats, TDataDrillingDepartmentStats, TDataDrillingStopeStats, TDataDrillingTotalStats, TDateCrushingOperationPeriodStats, TDateCrushingOperationStats } from './model'

export async function apiDataTunnelingExport(query: TDataTunneling): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/data/tunneling/export',
    method: 'post',
    params: query
  }); 
}


export async function apiDataTunnelingBatch(data: TDataTunnelingBatchDto[]): Promise<string> {
  return request<{ data: string }>({
    url: '/data/tunneling/batch',
    method: 'post',
    data
  }).then(res => res.data); 
}


export async function apiDataOutExport(query: TDataMuckingOut): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/data/out/export',
    method: 'post',
    params: query
  }); 
}


export async function apiDataOutBatch(data: TDataMuckingOutBatchDto[]): Promise<string> {
  return request<{ data: string }>({
    url: '/data/out/batch',
    method: 'post',
    data
  }).then(res => res.data); 
}


export async function apiDataOrepassExport(query: TDataOrepassOperation): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/data/orepass/export',
    method: 'post',
    params: query
  }); 
}


export async function apiDataOrepassBatch(data: TDataOrepassOperationBatchDto[]): Promise<string> {
  return request<{ data: string }>({
    url: '/data/orepass/batch',
    method: 'post',
    data
  }).then(res => res.data); 
}


export async function apiDataHoistingExport(query: TDataMineHoisting): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/data/hoisting/export',
    method: 'post',
    params: query
  }); 
}


export async function apiDataHoistingBatch(data: TDataMineHoistingBatchDto[]): Promise<string> {
  return request<{ data: string }>({
    url: '/data/hoisting/batch',
    method: 'post',
    data
  }).then(res => res.data); 
}


export async function apiDataFillingExport(query: TDataFilling): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/data/filling/export',
    method: 'post',
    params: query
  }); 
}


export async function apiDataFillingBatch(data: TDataFillingBatchDto[]): Promise<string> {
  return request<{ data: string }>({
    url: '/data/filling/batch',
    method: 'post',
    data
  }).then(res => res.data); 
}


export async function apiDataDrillingExport(query: TDataDrilling): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/data/drilling/export',
    method: 'post',
    params: query
  }); 
}


export async function apiDataDrillingBatch(data: TDataDrillingBatchDto[]): Promise<string> {
  return request<{ data: string }>({
    url: '/data/drilling/batch',
    method: 'post',
    data
  }).then(res => res.data); 
}


export async function apiDataDataEquipmentExport(query: TDataEquipment): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/data/dataEquipment/export',
    method: 'post',
    params: query
  }); 
}


export async function apiDataCrushingExport(query: TDataCrushingOperation): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/data/crushing/export',
    method: 'post',
    params: query
  }); 
}


export async function apiDataCrushingBatch(data: TDataCrushingOperationBatchDto[]): Promise<string> {
  return request<{ data: string }>({
    url: '/data/crushing/batch',
    method: 'post',
    data
  }).then(res => res.data); 
}


export async function apiDataStatsTunneling01b(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataTunnelingDepartmentWithPlanStats[]> {
  return request<{ data: TDataTunnelingDepartmentWithPlanStats[] }>({
    url: '/data/stats/tunneling/01b',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsTunneling01a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataTunnelingTotalWithPlanStats[]> {
  return request<{ data: TDataTunnelingTotalWithPlanStats[] }>({
    url: '/data/stats/tunneling/01a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsSupport01b(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataSupportDepartmentWithPlanStats[]> {
  return request<{ data: TDataSupportDepartmentWithPlanStats[] }>({
    url: '/data/stats/support/01b',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsSupport01a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataSupportTotalWithPlanStats[]> {
  return request<{ data: TDataSupportTotalWithPlanStats[] }>({
    url: '/data/stats/support/01a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsShotcrete01b(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataSupportTypeDepartmentWithPlanStats[]> {
  return request<{ data: TDataSupportTypeDepartmentWithPlanStats[] }>({
    url: '/data/stats/shotcrete/01b',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsShotcrete01a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataSupportTypeTotalWithPlanStats[]> {
  return request<{ data: TDataSupportTypeTotalWithPlanStats[] }>({
    url: '/data/stats/shotcrete/01a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsOut02(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataMuckingOutTotalWithPlanStats[]> {
  return request<{ data: TDataMuckingOutTotalWithPlanStats[] }>({
    url: '/data/stats/out/02',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsOut01e(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataMuckingOutPeriodStats[]> {
  return request<{ data: TDataMuckingOutPeriodStats[] }>({
    url: '/data/stats/out/01e',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsOut01c(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataMuckingOutDepartmentStats[]> {
  return request<{ data: TDataMuckingOutDepartmentStats[] }>({
    url: '/data/stats/out/01c',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsOut01b(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataMuckingOutStopeStats[]> {
  return request<{ data: TDataMuckingOutStopeStats[] }>({
    url: '/data/stats/out/01b',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsOut01a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataMuckingOutTotalStats[]> {
  return request<{ data: TDataMuckingOutTotalStats[] }>({
    url: '/data/stats/out/01a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsOrepass01d(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataOrepassOperationOrepassStats[]> {
  return request<{ data: TDataOrepassOperationOrepassStats[] }>({
    url: '/data/stats/orepass/01d',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsOrepass01c(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataOrepassOperationPeriodStats[]> {
  return request<{ data: TDataOrepassOperationPeriodStats[] }>({
    url: '/data/stats/orepass/01c',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsOrepass01b(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataOrepassOperationDepartmentStats[]> {
  return request<{ data: TDataOrepassOperationDepartmentStats[] }>({
    url: '/data/stats/orepass/01b',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsOrepass01a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataOrepassOperationStats[]> {
  return request<{ data: TDataOrepassOperationStats[] }>({
    url: '/data/stats/orepass/01a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsHoisting04b(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataMineHoistingPeriodStats[]> {
  return request<{ data: TDataMineHoistingPeriodStats[] }>({
    url: '/data/stats/hoisting/04b',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsHoisting04a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataMineHoistingStats[]> {
  return request<{ data: TDataMineHoistingStats[] }>({
    url: '/data/stats/hoisting/04a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsHoisting02b(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataMineHoistingPeriodStats[]> {
  return request<{ data: TDataMineHoistingPeriodStats[] }>({
    url: '/data/stats/hoisting/02b',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsHoisting02a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataMineHoistingStats[]> {
  return request<{ data: TDataMineHoistingStats[] }>({
    url: '/data/stats/hoisting/02a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsHoisting01b(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataMineHoistingPeriodStats[]> {
  return request<{ data: TDataMineHoistingPeriodStats[] }>({
    url: '/data/stats/hoisting/01b',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsHoisting01a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataMineHoistingStats[]> {
  return request<{ data: TDataMineHoistingStats[] }>({
    url: '/data/stats/hoisting/01a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsFilling01b(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataFillingStopeStats[]> {
  return request<{ data: TDataFillingStopeStats[] }>({
    url: '/data/stats/filling/01b',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsFilling01a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataFillingTotalWithPlanStats[]> {
  return request<{ data: TDataFillingTotalWithPlanStats[] }>({
    url: '/data/stats/filling/01a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsDth01b(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataDrillingDepartmentWithPlanStats[]> {
  return request<{ data: TDataDrillingDepartmentWithPlanStats[] }>({
    url: '/data/stats/dth/01b',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsDth01a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataDrillingTotalWithPlanStats[]> {
  return request<{ data: TDataDrillingTotalWithPlanStats[] }>({
    url: '/data/stats/dth/01a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsDrilling02(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataDrillingTotalWithPlanStats[]> {
  return request<{ data: TDataDrillingTotalWithPlanStats[] }>({
    url: '/data/stats/drilling/02',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsDrilling01g(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataDrillingTypeStats[]> {
  return request<{ data: TDataDrillingTypeStats[] }>({
    url: '/data/stats/drilling/01g',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsDrilling01f(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataDrillingWorkingFaceStats[]> {
  return request<{ data: TDataDrillingWorkingFaceStats[] }>({
    url: '/data/stats/drilling/01f',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsDrilling01e(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataDrillingPeriodStats[]> {
  return request<{ data: TDataDrillingPeriodStats[] }>({
    url: '/data/stats/drilling/01e',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsDrilling01c(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataDrillingDepartmentStats[]> {
  return request<{ data: TDataDrillingDepartmentStats[] }>({
    url: '/data/stats/drilling/01c',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsDrilling01b(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataDrillingStopeStats[]> {
  return request<{ data: TDataDrillingStopeStats[] }>({
    url: '/data/stats/drilling/01b',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsDrilling01a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataDrillingTotalStats[]> {
  return request<{ data: TDataDrillingTotalStats[] }>({
    url: '/data/stats/drilling/01a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsDeephole01b(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataDrillingDepartmentWithPlanStats[]> {
  return request<{ data: TDataDrillingDepartmentWithPlanStats[] }>({
    url: '/data/stats/deephole/01b',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsDeephole01a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataDrillingTotalWithPlanStats[]> {
  return request<{ data: TDataDrillingTotalWithPlanStats[] }>({
    url: '/data/stats/deephole/01a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsCrushing03a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDateCrushingOperationPeriodStats[]> {
  return request<{ data: TDateCrushingOperationPeriodStats[] }>({
    url: '/data/stats/crushing/03a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsCrushing02b(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDateCrushingOperationPeriodStats[]> {
  return request<{ data: TDateCrushingOperationPeriodStats[] }>({
    url: '/data/stats/crushing/02b',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsCrushing02a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDateCrushingOperationStats[]> {
  return request<{ data: TDateCrushingOperationStats[] }>({
    url: '/data/stats/crushing/02a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsCrushing01b(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDateCrushingOperationPeriodStats[]> {
  return request<{ data: TDateCrushingOperationPeriodStats[] }>({
    url: '/data/stats/crushing/01b',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsCrushing01a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDateCrushingOperationStats[]> {
  return request<{ data: TDateCrushingOperationStats[] }>({
    url: '/data/stats/crushing/01a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsBoltmesh01b(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataSupportTypeDepartmentWithPlanStats[]> {
  return request<{ data: TDataSupportTypeDepartmentWithPlanStats[] }>({
    url: '/data/stats/boltmesh/01b',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiDataStatsBoltmesh01a(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TDataSupportTypeTotalWithPlanStats[]> {
  return request<{ data: TDataSupportTypeTotalWithPlanStats[] }>({
    url: '/data/stats/boltmesh/01a',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


