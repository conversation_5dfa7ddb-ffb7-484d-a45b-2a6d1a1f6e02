<script setup name="PlanDriftMonthly" lang="ts">
import getSearchConfig from './config/searchConfig'
import getContentConfig from './config/contentConfig'
import getDialogConfig from './config/dialogConfig'
import useDialog from '@/hooks/useDialog'
import getComputedConfig from '@/hooks/getPageConfig'
import { planBaseUrl } from '@/api/config/base.js'
import { proxy } from '@/utils/provide'
import { DictOptions, getFaceList, getProjectDepartmentList, getStopeList } from '@/dict'
import MonthlyAddDialog from './components/MonthlyAddDialog.vue'
import ExcelStepImport from '@/components/ExcelStepImport'

const { priority_status } = proxy.useDict('priority_status');

const pageName = 'planDriftMonthly'
const requestBaseUrl = planBaseUrl
const pageSearchRef = useTemplateRef('pageSearchRef')
const pageContentRef = useTemplateRef('pageContentRef')
const dialogHideItems = ref<string[]>([])
const tableHideItems = ref<string[]>([])
const projectOptions = ref<DictOptions[]>([])
const stopeOptions = ref<DictOptions[]>([])
const faceOptions = ref<DictOptions[]>([])

const initDict = async () => {
  projectOptions.value = await getProjectDepartmentList()
  stopeOptions.value = await getStopeList()
  faceOptions.value = await getFaceList()
}
initDict()
const dictMap = ref({
  projectDepartmentId:projectOptions,
  stopeId: stopeOptions,
  workingFaceId: faceOptions
})

const searchConfig = getSearchConfig()
const searchConfigComputed = computed(() => {
  return getComputedConfig(searchConfig, dictMap)
})
const tableSelected = ref<any[]>([])
const tableListener = {
  selectionChange: (selected:any) => {
    tableSelected.value = selected
  },
}
const contentConfig = getContentConfig()
const contentConfigComputed = computed(() => {
  contentConfig.hideItems = tableHideItems
  return contentConfig
})

const dialogConfig = getDialogConfig()

const dialogConfigComputed = computed(() => {
  dialogConfig.hideItems = dialogHideItems
  return getComputedConfig(dialogConfig, dictMap)
})

const addCallBack = () => {
  dialogHideItems.value.length = 0
}
const editCallBack = (_item: any, type: any, res: any) => {
  isEditMore.value = type
}
const isEditMore = ref(false)
const editMoreClick = () => {
  if (tableSelected.value.length > 0) {
    const data = tableSelected.value[0]
    pageContentRef.value?.editClick(data, true)
    nextTick(() => {
      const newArray = tableSelected.value.slice(1)
      dialogRef.value?.changeSelected(newArray)
    })
  }
}

const editNext = (data: any) => {
  pageContentRef.value?.editClick(data, true)
}

const {dialogRef, infoInit, addClick, editBtnClick} = useDialog(
  addCallBack,
  editCallBack,
  '添加'
)

const dialogWidth = ref('700px')
const searchData = computed(() => {
  return pageContentRef.value?.finalSearchData
})

const search = () => {
  pageSearchRef.value?.search()
}

const beforeSend = (queryInfo: anyObj) => {
}

const permission = ref({
  add: 'plan:planDriftMonthly:add',
  edit: 'plan:planDriftMonthly:edit',
  del: 'plan:planDriftMonthly:remove',
})

const onChangeShowColumn = (filterArr: string[]) => {
  tableHideItems.value = filterArr
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy.download(
    'plan/planDriftMonthly/export',
    {
      ...searchData.value,
    },
    `掘进月度计划_${new Date().getTime()}.xlsx`
  )
}

// 按月新增 dialog control
const monthlyAddDialogVisible = ref(false)

const openMonthlyAddDialog = () => {
  monthlyAddDialogVisible.value = true
}

// Excel import dialog control
const importVisible = ref(false)
const handleImport = () => {
  importVisible.value = true
}

const handleImportSuccess = () => {
  search()
}

const handleImportError = (error: any) => {
  console.error('Import error:', error)
}

const refreshData = () => {
  search()
}
</script>
<template>
  <div class="default-main page">
    <PageSearch
      ref="pageSearchRef"
      :pageName="pageName"
      :searchConfig="searchConfigComputed"
    ></PageSearch>
    <PageContent
      ref="pageContentRef"
      :pageName="pageName"
      :contentConfig="contentConfigComputed"
      :dictMap="dictMap"
      :tableListener="tableListener"
      :tableSelected="tableSelected"
      :permission="permission"
      :requestBaseUrl="requestBaseUrl"
      @beforeSend="beforeSend"
      @addClick="addClick"
      @editBtnClick="editBtnClick"
      @onChangeShowColumn="onChangeShowColumn"
      @editMoreClick="editMoreClick"
    >
      <template #handleLeft>
        <el-button
          type="primary"
          @click="openMonthlyAddDialog"
          v-hasPermi="[permission.add]"
        >
          <SvgIcon :size="14" iconClass="plus"></SvgIcon>
          <span class="ml6">按月添加</span>
        </el-button>
        <el-button
          class="order16"
          type="success"
          v-hasPermi="['plan:planDriftMonthly:add']"
          @click="handleImport"
        >
          <SvgIcon size="14" iconClass="upload" />
          <span class="ml6">导入</span>
        </el-button>
        <el-button
          class="order17"
          type="warning"
          v-hasPermi="['plan:planDriftMonthly:export']"
          @click="handleExport"
        >
          <SvgIcon size="14" iconClass="download" />
          <span class="ml6">导出</span>
        </el-button>
      </template>
    </PageContent>
    <PageDialog
      ref="dialogRef"
      sendIdKey="id"
      :width="getWidth(dialogWidth)"
      :pageName="pageName"
      :dialogConfig="dialogConfigComputed"
      :infoInit="infoInit"
      :search="search"
      :isEditMore="isEditMore"
      :requestBaseUrl="requestBaseUrl"
      @editNext="editNext"
    />

    <MonthlyAddDialog
      v-model="monthlyAddDialogVisible"
      :projectOptions="projectOptions"
      :faceOptions="faceOptions"
      :stopeOptions="stopeOptions"
      :priorityOptions="priority_status"
      @refresh="refreshData"
    />
    <ExcelStepImport
      v-model:visible="importVisible"
      templateKey="plan_drift_monthly"
      title="掘进月度计划导入"
      @success="handleImportSuccess"
      @error="handleImportError"
    />
  </div>
</template>

<style scoped lang="scss">
</style>
