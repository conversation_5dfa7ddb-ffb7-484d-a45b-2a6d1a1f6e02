<script setup name="BaseEquipment" lang="ts">
import getSearchConfig from './config/searchConfig'
import getContentConfig from './config/contentConfig'
import getDialogConfig from './config/dialogConfig'
import useDialog from '@/hooks/useDialog'
import getComputedConfig from '@/hooks/getPageConfig'
import { baseBaseUrl } from '@/api/config/base.js'
import { proxy } from '@/utils/provide'
import { FormItem } from '@/BaseComponent/BaseForm'

// 设备类型选择
// const equipmentTypeOptions = [
//   { label: '电机车', value: 1 },
//   { label: '旋回破碎机', value: 2 },
//   { label: '主井提升机', value: 3 },
//   { label: '圆锥破碎机', value: 4 },
//   { label: '高压辊磨机', value: 5 },
//   { label: '球磨机', value: 6 },
//   { label: '立磨机', value: 7 },
//   { label: '精矿过滤机', value: 8 },
//   { label: '尾矿过滤机', value: 9 },
// ]

const { base_usage_status } = proxy.useDict('base_usage_status')

const pageName = 'baseEquipment'
const requestBaseUrl = baseBaseUrl
const pageSearchRef = useTemplateRef('pageSearchRef')
const pageContentRef = useTemplateRef('pageContentRef')
const dialogHideItems = ref<string[]>([])
const tableHideItems = ref<string[]>([])

const equipmentTypeMap = {
  '1': '电机车',
  '2': '旋回破碎机',
  '3': '主井提升机',
  '4': '圆锥破碎机',
  '5': '高压辊磨机',
  '6': '球磨机',
  '7': '立磨机',
  '8': '精矿过滤机',
  '9': '尾矿过滤机',
} as Record<string, string>

const equipmentType = 4
const equipmentName = equipmentTypeMap[equipmentType.toString()] || ''
const contentFields = [
  'id', // 设备数据ID
  'equipmentNo', // 设备编号
  // 'mineCarsNumber', // 矿车数量
  'unitEfficiency', // 单位效率
  'unit', // 单位
  'status', // 状态
  'startTime', // 开始时间
  'endTime', // 结束时间
  'todo', // 操作
]
const dialogFields = [
  'equipmentNo', // 设备编号,
  // 'mineCarsNumber', // 矿车数量,
  'unitEfficiency', // 单位效率,
  'unit', // 单位,
  'status', // 状态,
  'startTime', // 开始时间,
  'endTime', // 结束时间,
]

const dictMap = ref({
  status: base_usage_status,
})

const searchConfig = getSearchConfig()
const searchConfigComputed = computed(() => {
  return getComputedConfig(searchConfig, dictMap)
})
const tableSelected = ref<any[]>([])
const tableListener = {
  selectionChange: (selected: any) => {
    tableSelected.value = selected
  },
}
const contentConfig = getContentConfig()
const contentConfigComputed = computed(() => {
  contentConfig.hideItems = contentConfig.tableItem
    .filter((item: any) => !contentFields.includes(item.prop))
    .map((item: any) => item.prop)
  return contentConfig
})

const dialogConfig = getDialogConfig()

const dialogConfigComputed = computed(() => {
  dialogConfig.hideItems = dialogConfig.formItems
    .filter((item: FormItem) => !dialogFields.includes(item.field))
    .map((item: FormItem) => item.field)
  return getComputedConfig(dialogConfig, dictMap)
})

const addCallBack = () => {
  dialogHideItems.value.length = 0
  // 设置默认的设备类型
  nextTick(() => {
    dialogRef.value?.setFormData('equipmentType', equipmentType)
  })
}
const editCallBack = (_item: any, type: any, res: any) => {
  isEditMore.value = type
}
const isEditMore = ref(false)
const editMoreClick = () => {
  if (tableSelected.value.length > 0) {
    const data = tableSelected.value[0]
    pageContentRef.value?.editClick(data, true)
    nextTick(() => {
      const newArray = tableSelected.value.slice(1)
      dialogRef.value?.changeSelected(newArray)
    })
  }
}

const editNext = (data: any) => {
  pageContentRef.value?.editClick(data, true)
}

const { dialogRef, infoInit, addClick, editBtnClick } = useDialog(
  addCallBack,
  editCallBack,
  '添加'
)

const dialogWidth = ref('700px')
// const searchData = computed(() => {
//   return pageContentRef.value?.finalSearchData
// })

const search = () => {
  pageSearchRef.value?.search()
}

const beforeSend = (queryInfo: anyObj) => {
  // 添加设备类型参数
  queryInfo.equipmentType = equipmentType

  // Process 开始时间 date range
  if (queryInfo.startTime && Array.isArray(queryInfo.startTime)) {
    const dateRange = queryInfo.startTime
    queryInfo['params[beginStartTime]'] = dateRange[0]
    queryInfo['params[endStartTime]'] = dateRange[1]
    delete queryInfo.startTime
  }
  // Process 结束时间 date range
  if (queryInfo.endTime && Array.isArray(queryInfo.endTime)) {
    const dateRange = queryInfo.endTime
    queryInfo['params[beginEndTime]'] = dateRange[0]
    queryInfo['params[endEndTime]'] = dateRange[1]
    delete queryInfo.endTime
  }
}

const permission = ref({
  add: 'base:baseEquipment:add',
  edit: 'base:baseEquipment:edit',
  del: 'base:baseEquipment:remove',
})

const onChangeShowColumn = (filterArr: string[]) => {
  tableHideItems.value = filterArr
}

// 设备类型改变时的处理
// const onEquipmentTypeChange = () => {
//   // 重新查询数据
//   search()
// }
</script>
<template>
  <div class="default-main page">
    <h2 class="p-4">{{ equipmentName }}</h2>

    <!-- 设备类型选择 -->
    <!-- <div class="mb-4 p-4 bg-white rounded shadow">
      <div class="flex items-center gap-4">
        <label class="text-sm font-medium text-gray-700">设备类型：</label>
        <el-select
          v-model="equipmentType"
          placeholder="请选择设备类型"
          style="width: 200px"
          @change="onEquipmentTypeChange"
        >
          <el-option
            v-for="option in equipmentTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </div>
    </div> -->

    <PageSearch
      ref="pageSearchRef"
      :pageName="pageName"
      :searchConfig="searchConfigComputed"
    ></PageSearch>
    <PageContent
      ref="pageContentRef"
      :pageName="pageName"
      :contentConfig="contentConfigComputed"
      :dictMap="dictMap"
      :tableListener="tableListener"
      :tableSelected="tableSelected"
      :permission="permission"
      :requestBaseUrl="requestBaseUrl"
      @beforeSend="beforeSend"
      @addClick="addClick"
      @editBtnClick="editBtnClick"
      @onChangeShowColumn="onChangeShowColumn"
      @editMoreClick="editMoreClick"
    >
    </PageContent>
    <PageDialog
      ref="dialogRef"
      sendIdKey="id"
      :width="getWidth(dialogWidth)"
      :pageName="pageName"
      :dialogConfig="dialogConfigComputed"
      :infoInit="infoInit"
      :search="search"
      :isEditMore="isEditMore"
      :requestBaseUrl="requestBaseUrl"
      @editNext="editNext"
      :otherInfo="{
        equipmentType: equipmentType,
      }"
    />
  </div>
</template>

<style scoped lang="scss"></style>
