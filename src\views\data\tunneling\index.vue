<script setup name="Tunneling" lang="ts">
import getSearchConfig from './config/searchConfig'
import getContentConfig from './config/contentConfig'
import getDialogConfig from './config/dialogConfig'
import useDialog from '@/hooks/useDialog'
import getComputedConfig from '@/hooks/getPageConfig'
import { dataBaseUrl } from '@/api/config/base.js'
import { proxy } from '@/utils/provide'
import { DictOptions, getPeriodList, getProjectDepartmentList, getFaceList, getStopeList } from '@/dict'
import BatchAddDialog from './components/BatchAddDialog.vue'
import { Plus } from '@element-plus/icons-vue'

const pageName = 'tunneling'
const requestBaseUrl = dataBaseUrl
const pageSearchRef = useTemplateRef('pageSearchRef')
const pageContentRef = useTemplateRef('pageContentRef')
const dialogHideItems = ref<string[]>([])
const tableHideItems = ref<string[]>([])
const periodOptions = ref<DictOptions[]>([])
const projectDepartmentOptions = ref<DictOptions[]>([])
const workingFaceOptions = ref<DictOptions[]>([])
const stopeOptions = ref<DictOptions[]>([])

const initDict = async () => {
  periodOptions.value = await getPeriodList()
  projectDepartmentOptions.value = await getProjectDepartmentList()
  workingFaceOptions.value = await getFaceList()
  stopeOptions.value = await getStopeList()
}
initDict()

const dictMap = ref({
  workingPeriodId: periodOptions,
  projectDepartmentId: projectDepartmentOptions,
  workingFaceId: workingFaceOptions,
  stopeId: stopeOptions,
})

const searchConfig = getSearchConfig()
const searchConfigComputed = computed(() => {
  return getComputedConfig(searchConfig, dictMap)
})
const tableSelected = ref<any[]>([])
const tableListener = {
  selectionChange: (selected:any) => {
    tableSelected.value = selected
  },
}
const contentConfig = getContentConfig()
const contentConfigComputed = computed(() => {
  contentConfig.hideItems = tableHideItems
  return contentConfig
})

const dialogConfig = getDialogConfig()

const dialogConfigComputed = computed(() => {
  dialogConfig.hideItems = dialogHideItems
  return getComputedConfig(dialogConfig, dictMap)
})

const addCallBack = () => {
  dialogHideItems.value.length = 0
}
const editCallBack = (_item: any, type: any, res: any) => {
  isEditMore.value = type
}
const isEditMore = ref(false)
const editMoreClick = () => {
  if (tableSelected.value.length > 0) {
    const data = tableSelected.value[0]
    pageContentRef.value?.editClick(data, true)
    nextTick(() => {
      const newArray = tableSelected.value.slice(1)
      dialogRef.value?.changeSelected(newArray)
    })
  }
}

const editNext = (data: any) => {
  pageContentRef.value?.editClick(data, true)
}

const {dialogRef, infoInit, addClick, editBtnClick} = useDialog(
  addCallBack,
  editCallBack,
  '添加'
)

const dialogWidth = ref('700px')
const searchData = computed(() => {
  return pageContentRef.value?.finalSearchData
})

const search = () => {
  pageSearchRef.value?.search()
}

const beforeSend = (queryInfo: anyObj) => {
  // Process 作业日期 date range
  if (queryInfo.operationDate && Array.isArray(queryInfo.operationDate)) {
    const dateRange = queryInfo.operationDate
    queryInfo['params[beginOperationDate]'] = dateRange[0]
    queryInfo['params[endOperationDate]'] = dateRange[1]
    delete queryInfo.operationDate
  }
}

const permission = ref({
  add: 'data:tunneling:add',
  edit: 'data:tunneling:edit',
  del: 'data:tunneling:remove',
})

const onChangeShowColumn = (filterArr: string[]) => {
  tableHideItems.value = filterArr
}

// 批量添加相关
const batchAddDialogVisible = ref(false)

const openBatchAddDialog = () => {
  batchAddDialogVisible.value = true
}

const handleBatchAddRefresh = () => {
  search()
}
</script>
<template>
  <div class="default-main page">
    <PageSearch
      ref="pageSearchRef"
      :pageName="pageName"
      :searchConfig="searchConfigComputed"
    ></PageSearch>
    <PageContent
      ref="pageContentRef"
      :pageName="pageName"
      :contentConfig="contentConfigComputed"
      :dictMap="dictMap"
      :tableListener="tableListener"
      :tableSelected="tableSelected"
      :permission="permission"
      :requestBaseUrl="requestBaseUrl"
      @beforeSend="beforeSend"
      @addClick="addClick"
      @editBtnClick="editBtnClick"
      @onChangeShowColumn="onChangeShowColumn"
      @editMoreClick="editMoreClick"
    >
      <template #headerBtns>
        <el-button type="success" @click="openBatchAddDialog">
          <el-icon><Plus /></el-icon> 批量添加
        </el-button>
      </template>
    </PageContent>
    <PageDialog
      ref="dialogRef"
      sendIdKey="id"
      :width="getWidth(dialogWidth)"
      :pageName="pageName"
      :dialogConfig="dialogConfigComputed"
      :infoInit="infoInit"
      :search="search"
      :isEditMore="isEditMore"
      :requestBaseUrl="requestBaseUrl"
      @editNext="editNext"
    />

    <!-- 批量添加对话框 -->
    <BatchAddDialog
      v-model="batchAddDialogVisible"
      :periodOptions="periodOptions"
      :projectDepartmentOptions="projectDepartmentOptions"
      :workingFaceOptions="workingFaceOptions"
      :stopeOptions="stopeOptions"
      @refresh="handleBatchAddRefresh"
    />

  </div>
</template>

<style scoped lang="scss">
</style>
