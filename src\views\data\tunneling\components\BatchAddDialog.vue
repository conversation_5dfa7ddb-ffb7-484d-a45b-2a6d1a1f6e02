<script setup lang="ts">
import { dataBaseUrl } from '@/api/config/base.js'
import { apiDataTunnelingBatch } from '@/apis/data'
import { request } from '@/utils/hsj/service/index'
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, ref, watch } from 'vue'

const pageName = 'tunneling'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  periodOptions: {
    type: Array,
    default: () => [],
  },
  projectDepartmentOptions: {
    type: Array,
    default: () => [],
  },
  workingFaceOptions: {
    type: Array,
    default: () => [],
  },
  stopeOptions: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const selectedDate = ref('')
const selectedProjectDepartment = ref('')

const tableData = ref<any[]>([])
const loading = ref(false)

const formRef = ref<any>(null)
const rules = {
  selectedDate: [{ required: true, message: '请选择日期', trigger: 'change' }],
  selectedProjectDepartment: [{ required: true, message: '请选择项目部门', trigger: 'change' }],
}

const fetchData = async () => {
  if (!selectedDate.value) {
    ElMessage.warning('请先选择日期')
    return
  }
  if (!selectedProjectDepartment.value) {
    ElMessage.warning('请先选择项目部门')
    return
  }

  loading.value = true
  try {
    const res = await request<any>({
      url: `${dataBaseUrl}/${pageName}/list`,
      method: 'get',
      params: {
        operationDate: selectedDate.value,
        projectDepartmentId: selectedProjectDepartment.value,
        pageSize: 999,
        orderByColumn: 'workingPeriodId',
        orderByType: 'asc',
      },
    })

    if (res.code === 200) {
      tableData.value =
        res.rows.map((item: any) => {
          return {
            ...item,
            workingPeriodId: String(item.workingPeriodId),
            projectDepartmentId: String(item.projectDepartmentId),
            workingFaceId: String(item.workingFaceId),
            stopeId: String(item.stopeId),
          }
        }) || []
    } else {
      ElMessage.error(res.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取掘进数据失败:', error)
    ElMessage.error('获取掘进数据失败')
  } finally {
    loading.value = false
  }
}

const addRow = () => {
  if (!selectedDate.value) {
    ElMessage.warning('请先选择日期')
    return
  }
  if (!selectedProjectDepartment.value) {
    ElMessage.warning('请先选择项目部门')
    return
  }

  tableData.value.push({
    operationDate: selectedDate.value,
    projectDepartmentId: selectedProjectDepartment.value,
    workingFaceId: '',
    stopeId: '',
    workingPeriodId: '',
    tunnelingLength: '',
    tunnelingVolume: '',
    workContent: '',
    remarks: '',
    isNew: true,
  })

  setTimeout(() => {
    const selectElements = document.querySelectorAll(
      '.batch-add-dialog .el-table .el-select'
    )
    if (selectElements && selectElements.length > 0) {
      const targetSelect = selectElements[selectElements.length - 1]
      if (targetSelect) {
        const input = targetSelect.querySelector('input')
        if (input) {
          ;(input as HTMLElement).focus()
        } else {
          ;(targetSelect as HTMLElement).click()
        }
      }
    }
  }, 100)
}

const deleteRow = (index: number, _row: any) => {
  tableData.value.splice(index, 1)
}

const handleDialogClosed = () => {
  // Clear form data when dialog is closed
  selectedDate.value = ''
  selectedProjectDepartment.value = ''
  tableData.value = []
}

const saveAll = async () => {
  if (!selectedDate.value) {
    ElMessage.warning('请先选择日期')
    return
  }
  if (!selectedProjectDepartment.value) {
    ElMessage.warning('请先选择项目部门')
    return
  }

  for (const item of tableData.value) {
    if (!item.workingFaceId) {
      ElMessage.warning('请选择工作面')
      return
    }
    if (!item.stopeId) {
      ElMessage.warning('请选择采场')
      return
    }
    if (!item.workingPeriodId) {
      ElMessage.warning('请选择作业时段')
      return
    }
    if (item.tunnelingLength === undefined || item.tunnelingLength === null || item.tunnelingLength === '') {
      ElMessage.warning('请输入掘进长度')
      return
    }
    if (item.tunnelingVolume === undefined || item.tunnelingVolume === null || item.tunnelingVolume === '') {
      ElMessage.warning('请输入掘进体积')
      return
    }
  }

  console.log('提交数据:', JSON.stringify(tableData.value, null, 2))

  loading.value = true
  try {
    // Prepare data for submission
    const submitData = tableData.value.map((item) => ({
      ...item,
      operationDate: selectedDate.value,
      projectDepartmentId: selectedProjectDepartment.value,
    }))

    // Call the API to save the data
    const res = await apiDataTunnelingBatch(submitData)
    
    if (res) {
      ElMessage.success('保存成功')
      dialogVisible.value = false
      emit('refresh')
    } else {
      ElMessage.error('保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

watch([selectedDate, selectedProjectDepartment], ([newDate, newDept]) => {
  if (newDate && newDept) {
    tableData.value = []
    fetchData()
  }
})

// 根据工作面过滤采场
const filteredStopeOptions = computed(() => {
  return props.stopeOptions.filter((stope: any) => {
    return !stope.parentValue || tableData.value.some(row => 
      row.workingFaceId === String(stope.parentValue)
    )
  })
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="按日添加掘进数据"
    destroy-on-close
    width="1400px"
    :close-on-click-modal="false"
    append-to-body
    class="batch-add-dialog"
    @closed="handleDialogClosed"
  >
    <el-form
      ref="formRef"
      :model="{ selectedDate, selectedProjectDepartment }"
      :rules="rules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="选择日期" prop="selectedDate">
            <el-date-picker
              v-model="selectedDate"
              type="date"
              placeholder="请选择日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目部门" prop="selectedProjectDepartment">
            <el-select
              v-model="selectedProjectDepartment"
              placeholder="请选择项目部门"
              style="width: 100%"
            >
              <el-option
                v-for="item in projectDepartmentOptions"
                :key="(item as any).value"
                :label="(item as any).label"
                :value="(item as any).value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item>
        <el-button type="success" @click="addRow">
          <el-icon><component :is="Plus" /></el-icon> 添加行
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      max-height="600px"
    >
      <el-table-column label="工作面" prop="workingFaceId" width="150">
        <template #default="{ row }">
          <el-select
            v-model="row.workingFaceId"
            placeholder="请选择工作面"
            style="width: 100%"
          >
            <el-option
              v-for="item in workingFaceOptions"
              :key="(item as any).value"
              :label="(item as any).label"
              :value="(item as any).value"
            />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column label="采场" prop="stopeId" width="150">
        <template #default="{ row }">
          <el-select
            v-model="row.stopeId"
            placeholder="请选择采场"
            style="width: 100%"
          >
            <el-option
              v-for="item in stopeOptions"
              :key="(item as any).value"
              :label="(item as any).label"
              :value="(item as any).value"
            />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column label="作业时段" prop="workingPeriodId" width="150">
        <template #default="{ row }">
          <el-select
            v-model="row.workingPeriodId"
            placeholder="请选择作业时段"
            style="width: 100%"
          >
            <el-option
              v-for="item in periodOptions"
              :key="(item as any).value"
              :label="(item as any).label"
              :value="(item as any).value"
            />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column label="掘进长度(m)" prop="tunnelingLength" width="120">
        <template #default="{ row }">
          <el-input v-model="row.tunnelingLength" placeholder="请输入掘进长度" />
        </template>
      </el-table-column>

      <el-table-column label="掘进体积(m³)" prop="tunnelingVolume" width="120">
        <template #default="{ row }">
          <el-input v-model="row.tunnelingVolume" placeholder="请输入掘进体积" />
        </template>
      </el-table-column>

      <el-table-column label="工作内容" prop="workContent" width="150">
        <template #default="{ row }">
          <el-input v-model="row.workContent" placeholder="请输入工作内容" />
        </template>
      </el-table-column>

      <el-table-column label="备注" prop="remarks" width="150">
        <template #default="{ row }">
          <el-input v-model="row.remarks" placeholder="请输入备注" />
        </template>
      </el-table-column>

      <el-table-column label="操作" width="100" fixed="right">
        <template #default="{ row, $index }">
          <el-button type="danger" link @click="deleteRow($index, row)">
            <el-icon><component :is="Delete" /></el-icon> 删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="saveAll" :loading="loading"
        >保 存</el-button
      >
    </template>
  </el-dialog>
</template>

<style>
.batch-add-dialog .el-dialog__body {
  padding: 20px !important;
}

.batch-add-dialog .el-table {
  margin-top: 15px;
  width: 100% !important;
}

.batch-add-dialog .el-dialog__header,
.batch-add-dialog .el-dialog__footer {
  padding: 15px 20px !important;
}

.batch-add-dialog .el-dialog__body .el-table__body-wrapper {
  overflow-y: auto;
}

.batch-add-dialog .el-form-item {
  margin-bottom: 18px;
}
</style>
