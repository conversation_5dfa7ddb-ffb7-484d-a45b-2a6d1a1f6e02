import request from '@/utils/request'

// 查询重点工程列表
export function listBasePriorityProject(query) {
  return request({
    url: '/base/basePriorityProject/list',
    method: 'get',
    params: query
  })
}

// 查询重点工程详细
export function getBasePriorityProject(id) {
  return request({
    url: '/base/basePriorityProject/' + id,
    method: 'get'
  })
}

// 新增重点工程
export function addBasePriorityProject(data) {
  return request({
    url: '/base/basePriorityProject',
    method: 'post',
    data: data
  })
}

// 修改重点工程
export function updateBasePriorityProject(data) {
  return request({
    url: '/base/basePriorityProject',
    method: 'put',
    data: data
  })
}

// 删除重点工程
export function delBasePriorityProject(id) {
  return request({
    url: '/base/basePriorityProject/' + id,
    method: 'delete'
  })
}
