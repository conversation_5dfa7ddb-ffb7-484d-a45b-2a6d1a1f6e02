// 自动生成的类型定义，请勿手动修改

export interface TPlanSupportMonthly {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  projectDepartmentId?: number;
  workingFaceId?: number;
  stopeId?: number;
  boltMeshSupportMeter?: any;
  shotcreteSupportMeter?: any;
  planDate?: string;
  boltMeshSupportVolume?: any;
  shotcreteSupportVolume?: any;
  supportVolume?: any;
  supportMeter?: any;
}

export interface TPlanStopeMonthly {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  stopeId?: number;
  planDate?: string;
  oreOutput?: string;
}

export interface TPlanMiningMonthly {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  projectDepartmentId?: number;
  driftMeter?: any;
  rawOreVolume?: any;
  supportMeter?: any;
  fillingVolume?: any;
  dthMeter?: any;
  deepHoleMeter?: any;
  oreOutputVolume?: any;
  planDate?: string;
}

export interface TPlanMineralSaleMonthly {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  ironConcentrateVolume?: any;
  concentratorBinsStockVolume?: any;
  serviceShaftSurfaceStockVolume?: any;
  rawOreGrade?: any;
  stockVolume?: any;
  planDate?: string;
}

export interface TPlanMineralMonthly {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  rawOreProcessingVolume?: any;
  drySeparationVolume?: any;
  grindingFeedVolume?: any;
  rawOreGrade?: any;
  concentrateGrade?: any;
  tailingGrade?: any;
  concentrateVolume?: any;
  planDate?: string;
}

export interface TPlanDthMonthly {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  projectDepartmentId?: number;
  workingFaceId?: number;
  stopeId?: number;
  dthMeter?: any;
  planDate?: string;
}

export interface TPlanDriftMonthly {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  projectDepartmentId?: number;
  workingFaceId?: number;
  stopeId?: number;
  driftMeter?: any;
  driftVolume?: any;
  planDate?: string;
}

export interface TPlanDeepHoleMonthly {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  projectDepartmentId?: number;
  workingFaceId?: number;
  stopeId?: number;
  deepHoleMeter?: any;
  planDate?: string;
}

export interface TPlanBackfillingMonthly {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  projectDepartmentId?: number;
  workingFaceId?: number;
  stopeId?: number;
  fillingVolume?: any;
  planDate?: string;
}

export interface TDataTunneling {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  operationDate?: string;
  projectDepartmentId?: number;
  workingFaceId?: number;
  stopeId?: number;
  workingPeriodId?: number;
  tunnelingLength?: any;
  tunnelingVolume?: any;
  workContent?: string;
  remarks?: string;
}

export interface TDataMuckingOut {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  operationDate?: string;
  projectDepartmentId?: number;
  stopeId?: number;
  workingPeriodId?: number;
  tons?: any;
}

export interface TDataOrepassOperation {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  projectDepartmentId?: number;
  orePassId?: number;
  workingPeriodId?: number;
  operationDate?: string;
  trips?: number;
  oreTons?: any;
}

export interface TDataMineHoisting {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  workingPeriodId?: number;
  operationDate?: string;
  operationTime?: number;
  faultTime?: number;
  buckets?: number;
  weight?: any;
  faultReason?: string;
  faultStartTime?: string;
  faultEndTime?: string;
}

export interface TDataFilling {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  operationDate?: string;
  projectDepartmentId?: number;
  stopeId?: number;
  workingPeriodId?: number;
  slurryVolume?: any;
  cementWeight?: any;
  fillingRatio?: string;
  fillingConcentration?: any;
  firstFillingTime?: string;
  endFillingTime?: string;
  remarks?: string;
}

export interface TDataDrilling {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  operationDate?: string;
  projectDepartmentId?: number;
  stopeId?: number;
  workingFaceId?: number;
  workingPeriodId?: number;
  drillingType?: string;
  progressMeters?: any;
}

export interface TDataEquipment {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  equipmentNo?: string;
  mineCarsNumber?: number;
  numberOfRunningTrains?: any;
  operationTime?: number;
  singleProcessingVolume?: any;
  totalProcessingVolume?: any;
  operationDate?: string;
  startTime?: string;
  endTime?: string;
  equipmentType?: number;
}

export interface TDataCrushingOperation {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  workingPeriodId?: number;
  operationDate?: string;
  operationTime?: number;
  crushingVolume?: any;
  faultTime?: number;
  faultReason?: string;
  faultStartTime?: string;
  faultEndTime?: string;
}

export interface TBaseStope {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  stopeId?: number;
  stopeName?: string;
  workingFaceId?: number;
  status?: number;
  startTime?: string;
  endTime?: string;
  isDelete?: number;
}

export interface TBaseProjectDepartment {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  projectDepartmentId?: number;
  projectDepartmentName?: string;
  status?: number;
  startTime?: string;
  endTime?: string;
  isDelete?: number;
}

export interface TBaseWorkingPeriod {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  workingPeriodId?: number;
  workingPeriodName?: string;
  status?: number;
  startTime?: string;
  endTime?: string;
  isDelete?: number;
}

export interface TBaseOrePass {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  orePassId?: number;
  orePassName?: string;
  status?: number;
  startTime?: string;
  endTime?: string;
  isDelete?: number;
}

export interface TBaseWorkingFace {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  workingFaceId?: number;
  workingFaceName?: string;
  status?: number;
  startTime?: string;
  endTime?: string;
  isDelete?: number;
}

export interface TBasePriorityProject {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  stopeId?: number;
  workingFaceId?: number;
  status?: number;
  startTime?: string;
  endTime?: string;
  isDelete?: number;
}

export interface TBaseEquipment {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  equipmentNo?: string;
  mineCarsNumber?: number;
  unitEfficiency?: any;
  unit?: string;
  status?: number;
  startTime?: string;
  endTime?: string;
  isDelete?: number;
  equipmentType?: number;
}

export interface TPlanSupportMonthlyBatchDto {
  id?: number;
  planMonth?: string;
  projectDepartmentId?: number;
  boltMeshSupportMeter?: any;
  shotcreteSupportMeter?: any;
  remark?: string;
  operationType?: string;
}

export interface TPlanStopeMonthlyBatchDto {
  id?: number;
  planMonth?: string;
  stopeId?: number;
  oreOutput?: string;
  remark?: string;
  operationType?: string;
}

export interface TPlanMiningMonthlyBatchDto {
  id?: number;
  planMonth?: string;
  projectDepartmentId?: number;
  driftMeter?: any;
  rawOreVolume?: any;
  supportMeter?: any;
  fillingVolume?: any;
  dthMeter?: any;
  deepHoleMeter?: any;
  oreOutputVolume?: any;
  remark?: string;
  operationType?: string;
}

export interface TPlanMineralSaleMonthlyBatchDto {
  id?: number;
  planMonth?: string;
  ironConcentrateVolume?: any;
  concentratorBinsStockVolume?: any;
  serviceShaftSurfaceStockVolume?: any;
  rawOreGrade?: any;
  stockVolume?: any;
  remark?: string;
  operationType?: string;
}

export interface TPlanMineralMonthlyBatchDto {
  id?: number;
  planMonth?: string;
  oreQuantity?: any;
  oreGrade?: any;
  goldOutput?: any;
  crushingQuantity?: any;
  grindingQuantity?: any;
  flotationQuantity?: any;
  remark?: string;
  operationType?: string;
}

export interface TPlanDthMonthlyBatchDto {
  id?: number;
  planMonth?: string;
  projectDepartmentId?: number;
  workingFaceId?: number;
  stopeId?: number;
  dthMeter?: any;
  remark?: string;
  operationType?: string;
}

export interface TPlanDriftMonthlyBatchDto {
  id?: number;
  planMonth?: string;
  driftDistance?: any;
  departmentId?: number;
  rockVolume?: any;
  workingFaceId?: number;
  stopeId?: number;
  remark?: string;
  operationType?: string;
}

export interface TPlanDeepHoleMonthlyBatchDto {
  id?: number;
  planMonth?: string;
  projectDepartmentId?: number;
  workingFaceId?: number;
  stopeId?: number;
  deepHoleMeter?: any;
  remark?: string;
  operationType?: string;
}

export interface TPlanBackfillingMonthlyBatchDto {
  id?: number;
  planMonth?: string;
  projectDepartmentId?: number;
  workingFaceId?: number;
  stopeId?: number;
  fillingVolume?: any;
  remark?: string;
  operationType?: string;
}

export interface TDataTunnelingBatchDto {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  operationDate?: string;
  projectDepartmentId?: number;
  workingFaceId?: number;
  stopeId?: number;
  workingPeriodId?: number;
  tunnelingLength?: any;
  tunnelingVolume?: any;
  workContent?: string;
  remarks?: string;
  isNew?: boolean;
  workingPeriodName?: string;
  projectDepartmentName?: string;
  stopeName?: string;
  workingFaceName?: string;
}

export interface TDataMuckingOutBatchDto {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  operationDate?: string;
  projectDepartmentId?: number;
  stopeId?: number;
  workingPeriodId?: number;
  tons?: any;
  isNew?: boolean;
  workingPeriodName?: string;
  projectDepartmentName?: string;
  stopeName?: string;
}

export interface TDataOrepassOperationBatchDto {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  projectDepartmentId?: number;
  orePassId?: number;
  workingPeriodId?: number;
  operationDate?: string;
  trips?: number;
  oreTons?: any;
  isNew?: boolean;
  workingPeriodName?: string;
  projectDepartmentName?: string;
  orePassName?: string;
}

export interface TDataMineHoistingBatchDto {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  workingPeriodId?: number;
  operationDate?: string;
  operationTime?: number;
  faultTime?: number;
  buckets?: number;
  weight?: any;
  faultReason?: string;
  faultStartTime?: string;
  faultEndTime?: string;
  isNew?: boolean;
  workingPeriodName?: string;
}

export interface TDataFillingBatchDto {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  operationDate?: string;
  projectDepartmentId?: number;
  stopeId?: number;
  workingPeriodId?: number;
  slurryVolume?: any;
  cementWeight?: any;
  fillingRatio?: string;
  fillingConcentration?: any;
  firstFillingTime?: string;
  endFillingTime?: string;
  remarks?: string;
  isNew?: boolean;
  workingPeriodName?: string;
  projectDepartmentName?: string;
  stopeName?: string;
}

export interface TDataDrillingBatchDto {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  operationDate?: string;
  projectDepartmentId?: number;
  stopeId?: number;
  workingFaceId?: number;
  workingPeriodId?: number;
  drillingType?: string;
  progressMeters?: any;
  isNew?: boolean;
  workingPeriodName?: string;
  projectDepartmentName?: string;
  stopeName?: string;
  workingFaceName?: string;
}

export interface TDataCrushingOperationBatchDto {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  workingPeriodId?: number;
  operationDate?: string;
  operationTime?: number;
  crushingVolume?: any;
  faultTime?: number;
  faultReason?: string;
  faultStartTime?: string;
  faultEndTime?: string;
  isNew?: boolean;
  workingPeriodName?: string;
}

export interface TDataTunnelingDepartmentWithPlanStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  projectDepartmentId?: number;
  projectDepartmentName?: string;
  totalTunnelingLength?: any;
  totalTunnelingVolume?: any;
  planTunnelingLength?: any;
}

export interface TDataTunnelingTotalWithPlanStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  totalTunnelingLength?: any;
  totalTunnelingVolume?: any;
  planTunnelingLength?: any;
}

export interface TDataSupportDepartmentWithPlanStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  projectDepartmentId?: number;
  projectDepartmentName?: string;
  totalSupportLength?: any;
  totalSupportVolume?: any;
  planSupportLength?: any;
  planSupportVolume?: any;
}

export interface TDataSupportTotalWithPlanStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  totalSupportLength?: any;
  totalSupportVolume?: any;
  planSupportLength?: any;
  planSupportVolume?: any;
}

export interface TDataSupportTypeDepartmentWithPlanStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  supportType?: string;
  projectDepartmentId?: number;
  projectDepartmentName?: string;
  totalSupportLength?: any;
  totalSupportVolume?: any;
  planSupportLength?: any;
  planSupportVolume?: any;
}

export interface TDataSupportTypeTotalWithPlanStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  supportType?: string;
  totalSupportLength?: any;
  totalSupportVolume?: any;
  planSupportLength?: any;
  planSupportVolume?: any;
}

export interface TDataMuckingOutTotalWithPlanStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  totalTons?: any;
  planTons?: any;
}

export interface TDataMuckingOutPeriodStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  workingPeriodId?: number;
  workingPeriodName?: string;
  totalTons?: any;
}

export interface TDataMuckingOutDepartmentStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  projectDepartmentId?: number;
  projectDepartmentName?: string;
  totalTons?: any;
}

export interface TDataMuckingOutStopeStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  stopeId?: number;
  stopeName?: string;
  totalTons?: any;
}

export interface TDataMuckingOutTotalStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  totalTons?: any;
}

export interface TDataOrepassOperationOrepassStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  orePassId?: number;
  orePassName?: string;
  totalTrips?: number;
  totalOreTons?: any;
}

export interface TDataOrepassOperationPeriodStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  workingPeriodId?: number;
  workingPeriodName?: string;
  totalTrips?: number;
  totalOreTons?: any;
}

export interface TDataOrepassOperationDepartmentStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  projectDepartmentId?: number;
  projectDepartmentName?: string;
  totalTrips?: number;
  totalOreTons?: any;
}

export interface TDataOrepassOperationStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  totalTrips?: number;
  totalOreTons?: any;
}

export interface TDataMineHoistingPeriodStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  workingPeriodId?: number;
  workingPeriodName?: string;
  totalOperationTime?: any;
  totalFaultTime?: any;
  totalBuckets?: number;
  totalWeight?: any;
}

export interface TDataMineHoistingStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  totalOperationTime?: any;
  totalFaultTime?: any;
  totalBuckets?: number;
  totalWeight?: any;
}

export interface TDataFillingStopeStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  stopeId?: number;
  stopeName?: string;
  totalSlurryVolume?: any;
  totalCementWeight?: any;
  avgFillingConcentration?: any;
  planSlurryVolume?: any;
}

export interface TDataFillingTotalWithPlanStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  totalSlurryVolume?: any;
  totalCementWeight?: any;
  avgFillingConcentration?: any;
  planSlurryVolume?: any;
}

export interface TDataDrillingDepartmentWithPlanStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  projectDepartmentId?: number;
  projectDepartmentName?: string;
  totalProgressMeters?: any;
  planProgressMeters?: any;
}

export interface TDataDrillingTotalWithPlanStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  totalProgressMeters?: any;
  planProgressMeters?: any;
}

export interface TDataDrillingTypeStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  drillingType?: string;
  drillingTypeName?: string;
  totalProgressMeters?: any;
}

export interface TDataDrillingWorkingFaceStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  workingFaceId?: number;
  workingFaceName?: string;
  totalProgressMeters?: any;
}

export interface TDataDrillingPeriodStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  workingPeriodId?: number;
  workingPeriodName?: string;
  totalProgressMeters?: any;
}

export interface TDataDrillingDepartmentStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  projectDepartmentId?: number;
  projectDepartmentName?: string;
  totalProgressMeters?: any;
}

export interface TDataDrillingStopeStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  stopeId?: number;
  stopeName?: string;
  totalProgressMeters?: any;
}

export interface TDataDrillingTotalStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  totalProgressMeters?: any;
}

export interface TDateCrushingOperationPeriodStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  workingPeriodId?: number;
  workingPeriodName?: string;
  totalOperationTime?: any;
  totalFaultTime?: any;
  faultCount?: number;
  totalCrushingVolume?: any;
}

export interface TDateCrushingOperationStats {
  year?: number;
  month?: number;
  weekNumber?: number;
  operationDate?: string;
  weekStartDate?: string;
  weekEndDate?: string;
  totalOperationTime?: any;
  totalFaultTime?: any;
  faultCount?: number;
  totalCrushingVolume?: any;
}

export interface TBasePriorityProjectVo {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  stopeId?: number;
  workingFaceId?: number;
  status?: number;
  startTime?: string;
  endTime?: string;
  isDelete?: number;
  stopeName?: string;
  workingFaceName?: string;
}
