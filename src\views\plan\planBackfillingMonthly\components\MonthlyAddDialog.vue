<script setup lang="ts">
import { planBaseUrl } from '@/api/config/base.js'
import { request } from '@/utils/hsj/service/index'
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, ref, watch } from 'vue'
import { apiPlanPlanBackfillingMonthlyBatch } from '@/apis/plan'
import type { TPlanBackfillingMonthlyBatchDto } from '@/apis/model.d'

const pageName = 'planBackfillingMonthly'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  projectOptions: {
    type: Array,
    default: () => [],
  },
  faceOptions: {
    type: Array,
    default: () => [],
  },
  stopeOptions: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const selectedMonth = ref('')
const selectedProjectDepartment = ref('') // 添加项目部筛选条件

const tableData = ref<any[]>([])
const loading = ref(false)

const formRef = ref<any>(null)
const rules = {
  selectedMonth: [{ required: true, message: '请选择月份', trigger: 'change' }],
  selectedProjectDepartment: [
    { required: true, message: '请选择项目部', trigger: 'change' },
  ],
}

const fetchPlans = async () => {
  if (!selectedMonth.value || !selectedProjectDepartment.value) {
    ElMessage.warning('请先选择月份和项目部')
    return
  }

  loading.value = true
  try {
    const res = await request<any>({
      url: `${planBaseUrl}/${pageName}/list`,
      method: 'get',
      params: {
        planDate: selectedMonth.value,
        projectDepartmentId: selectedProjectDepartment.value,
        pageSize: 999,
      },
    })

    if (res.code === 200) {
      tableData.value =
        res.rows.map((item: any) => {
          return {
            ...item,
            workingFaceId: String(item.workingFaceId),
            stopeId: String(item.stopeId),
          }
        }) || []
    } else {
      ElMessage.error(res.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取计划数据失败:', error)
    ElMessage.error('获取计划数据失败')
  } finally {
    loading.value = false
  }
}

const addRow = () => {
  if (!selectedMonth.value || !selectedProjectDepartment.value) {
    ElMessage.warning('请先选择月份和项目部')
    return
  }

  tableData.value.push({
    planDate: selectedMonth.value,
    projectDepartmentId: selectedProjectDepartment.value,
    workingFaceId: '',
    stopeId: '',
    fillingVolume: '',
    isNew: true,
  })

  setTimeout(() => {
    const selectElements = document.querySelectorAll(
      '.monthly-add-dialog .el-table .el-select'
    )
    if (selectElements && selectElements.length > 0) {
      const targetSelect = selectElements[selectElements.length - 1]
      if (targetSelect) {
        const input = targetSelect.querySelector('input')
        if (input) {
          ;(input as HTMLElement).focus()
        } else {
          ;(targetSelect as HTMLElement).click()
        }
      }
    }
  }, 100)
}

const deleteRow = (index: number, _row: any) => {
  tableData.value.splice(index, 1)
}

const handleDialogClosed = () => {
  selectedMonth.value = ''
  selectedProjectDepartment.value = ''
  tableData.value = []
}

const saveAll = async () => {
  if (!selectedMonth.value || !selectedProjectDepartment.value) {
    ElMessage.warning('请先选择月份和项目部')
    return
  }

  if (tableData.value.length === 0) {
    ElMessage.warning('没有数据需要保存')
    return
  }

  for (const item of tableData.value) {
    if (!item.workingFaceId) {
      ElMessage.warning('请选择工作面')
      return
    }
    if (!item.stopeId) {
      ElMessage.warning('请选择采场')
      return
    }
  }

  console.log('提交数据:', JSON.stringify(tableData.value, null, 2))

  loading.value = true
  try {
    // 准备提交数据
    const batchData: TPlanBackfillingMonthlyBatchDto[] = tableData.value.map(
      (item) => ({
        id: item.id,
        planMonth: selectedMonth.value,
        projectDepartmentId: Number(selectedProjectDepartment.value),
        workingFaceId: Number(item.workingFaceId),
        stopeId: Number(item.stopeId),
        fillingVolume: item.fillingVolume,
        operationType: item.isNew ? 'add' : 'edit',
        remark: item.remark || '',
      })
    )

    // 使用批量保存API
    await apiPlanPlanBackfillingMonthlyBatch(batchData)

    ElMessage.success('保存成功')
    dialogVisible.value = false
    emit('refresh')
  } catch (error) {
    console.error('保存数据失败:', error)
    ElMessage.error('保存数据失败')
  } finally {
    loading.value = false
  }
}

watch([selectedMonth, selectedProjectDepartment], ([newMonth, newProject]) => {
  if (newMonth && newProject) {
    tableData.value = []
    fetchPlans()
  }
})

const getAvailableStopeOptions = (currentRow: any) => {
  // 首先根据工作面ID过滤采场选项
  let filteredOptions = props.stopeOptions
  if (currentRow.workingFaceId) {
    filteredOptions = props.stopeOptions.filter(
      (item: any) =>
        (item as any).parentValue === Number(currentRow.workingFaceId)
    )
  }

  // 然后过滤掉已选择的采场（避免重复选择）
  const selectedStopeIds = tableData.value
    .filter((row) => row !== currentRow && row.stopeId)
    .map((row) => row.stopeId)

  return filteredOptions.filter(
    (item: any) => !selectedStopeIds.includes((item as any).value)
  )
}

// 当工作面改变时，清空采场选择
const handleWorkingFaceChange = (row: any) => {
  row.stopeId = ''
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="按月添加充填计划"
    destroy-on-close
    width="1000px"
    :close-on-click-modal="false"
    append-to-body
    class="monthly-add-dialog"
    @closed="handleDialogClosed"
  >
    <el-form
      ref="formRef"
      :model="{ selectedMonth, selectedProjectDepartment }"
      :rules="rules"
      label-width="80px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="选择月份" prop="selectedMonth">
            <el-date-picker
              v-model="selectedMonth"
              type="month"
              placeholder="请选择月份"
              format="YYYY-MM"
              value-format="YYYYMM"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目部" prop="selectedProjectDepartment">
            <el-select
              v-model="selectedProjectDepartment"
              placeholder="请选择项目部"
              style="width: 100%"
            >
              <el-option
                v-for="item in projectOptions"
                :key="(item as any).value"
                :label="(item as any).label"
                :value="(item as any).value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item>
        <el-button type="success" @click="addRow">
          <el-icon><component :is="Plus" /></el-icon> 添加行
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      max-height="600px"
    >
      <el-table-column label="工作面" prop="workingFaceId">
        <template #default="{ row }">
          <el-select
            v-model="row.workingFaceId"
            placeholder="请选择工作面"
            style="width: 100%"
            @change="handleWorkingFaceChange(row)"
          >
            <el-option
              v-for="item in faceOptions"
              :key="(item as any).value"
              :label="(item as any).label"
              :value="(item as any).value"
            />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column label="采场" prop="stopeId">
        <template #default="{ row }">
          <el-select
            v-model="row.stopeId"
            placeholder="请选择采场"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="item in getAvailableStopeOptions(row)"
              :key="(item as any).value"
              :label="(item as any).label"
              :value="(item as any).value"
            />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column label="充填方量" prop="fillingVolume">
        <template #default="{ row }">
          <el-input v-model="row.fillingVolume" placeholder="请输入充填方量" />
        </template>
      </el-table-column>

      <el-table-column label="操作" width="120">
        <template #default="{ row, $index }">
          <el-button type="danger" link @click="deleteRow($index, row)">
            <el-icon><component :is="Delete" /></el-icon> 删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="saveAll" :loading="loading"
        >保 存</el-button
      >
    </template>
  </el-dialog>
</template>

<style>
.monthly-add-dialog .el-dialog__body {
  padding: 20px !important;
}

.monthly-add-dialog .el-table {
  margin-top: 15px;
  width: 100% !important;
}

.monthly-add-dialog .el-dialog__header,
.monthly-add-dialog .el-dialog__footer {
  padding: 15px 20px !important;
}

.monthly-add-dialog .el-dialog__body .el-table__body-wrapper {
  overflow-y: auto;
}

.monthly-add-dialog .el-form-item {
  margin-bottom: 18px;
}
</style>
