export default ():BaseFormProps => {
  return {
    itemStyle: {
      padding: '0px 0px 0px 0px',
    },
    rules: {
      operationDate: [
        { required: true, message: "作业日期不能为空", trigger: "blur" }
      ],
      projectDepartmentId: [
        { required: true, message: '项目部门不能为空', trigger: 'change' },
      ],
      stopeId: [
        { required: true, message: '采场不能为空', trigger: 'change' },
      ],
      workingPeriodId: [
        { required: true, message: '作业时段不能为空', trigger: 'change' },
      ],
    },
    formItems: [
      {
        field: 'operationDate',
        label: '作业日期',
        config: {
          clearable: false,
          type: 'date',
          disabledDate: (time: Date) => {
            return time.getTime() > Date.now()
          },
        },
        type: 'datepicker',
      },
      {
        field: 'projectDepartmentId',
        type: 'select',
        options: [],
        label: '项目部门',
      },
      {
        field: 'stopeId',
        type: 'select',
        options: [],
        label: '采场',
      },
      {
        field: 'workingPeriodId',
        type: 'select',
        options: [],
        label: '作业时段',
      },
      {
        field: 'slurryVolume',
        type: 'input',
        label: '生产料浆量(m³)',
      },
      {
        field: 'cementWeight',
        type: 'input',
        label: '胶固粉量(t)',
      },
      {
        field: 'fillingRatio',
        type: 'input',
        label: '充填比例',
      },
      {
        field: 'fillingConcentration',
        type: 'input',
        label: '充填浓度(%)',
      },
      {
        field: 'firstFillingTime',
        type: 'custom',
        label: '首冲时间',
        slotNames: ['firstFillingTime'],
      },
      {
        field: 'endFillingTime',
        type: 'custom',
        label: '结束时间',
        slotNames: ['endFillingTime'],
      },
      {
        field: 'remarks',
        type: 'input',
        label: '备注',
      },
    ],
    colLayout: {
      xl: 24,
      lg: 24,
      md: 24,
      sm: 24,
      xs: 24,
    },
    elFormConfig: {
      labelWidth: '100px',
    },
  }
}
