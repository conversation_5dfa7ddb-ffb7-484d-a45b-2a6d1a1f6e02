export default ():BaseFormProps => {
  return {
    itemStyle: {
      padding: '0px 0px 0px 0px',
    },
    rules: {
      operationDate: [
        { required: true, message: "作业日期不能为空", trigger: "blur" }
      ],
      projectDepartmentId: [
        { required: true, message: '项目部门不能为空', trigger: 'change' },
      ],
      workingFaceId: [
        { required: true, message: '工作面不能为空', trigger: 'change' },
      ],
      stopeId: [
        { required: true, message: '采场不能为空', trigger: 'change' },
      ],
      workingPeriodId: [
        { required: true, message: '作业时段不能为空', trigger: 'change' },
      ],
    },
    formItems: [
      {
        field: 'operationDate',
        label: '作业日期',
        config: {
          clearable: false,
          type: 'date',
          disabledDate: (time: Date) => {
            return time.getTime() > Date.now()
          },
        },
        type: 'datepicker',
      },
      {
        field: 'projectDepartmentId',
        type: 'select',
        options: [],
        label: '项目部门',
      },
      {
        field: 'workingFaceId',
        type: 'select',
        options: [],
        label: '工作面',
      },
      {
        field: 'stopeId',
        type: 'select',
        options: [],
        label: '采场',
      },
      {
        field: 'workingPeriodId',
        type: 'select',
        options: [],
        label: '作业时段',
      },
      {
        field: 'tunnelingLength',
        type: 'input',
        label: '掘进长度(m)',
      },
      {
        field: 'tunnelingVolume',
        type: 'input',
        label: '掘进体积(m³)',
      },
      {
        field: 'workContent',
        type: 'input',
        label: '工作内容',
      },
      {
        field: 'remarks',
        type: 'input',
        label: '备注',
      },
    ],
    colLayout: {
      xl: 24,
      lg: 24,
      md: 24,
      sm: 24,
      xs: 24,
    },
    elFormConfig: {
      labelWidth: '100px',
    },
  }
}
