<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsFilling01b } from '@/apis/data'
import type { TDataFillingStopeStats } from '@/apis/model'

interface Props {
  date: {
    viewType: string
    date: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TDataFillingStopeStats[]>([])

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, date } = props.date
    if (!date) {
      return
    }

    const params = {
      viewType,
      startDate: date,
      endDate: date,
    }

    const res = await apiDataStatsFilling01b(params)
    chartData.value = res || []
  } catch (error) {
    console.error('获取充填按采场数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期变化
watch(() => props.date, () => {
  if (props.date?.date) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'value',
        name: '充填量(m³)',
      },
      yAxis: {
        type: 'category',
        data: [],
      },
      series: [],
    }
  }

  // 准备数据
  const stopeNames = chartData.value.map((item: TDataFillingStopeStats) => item.stopeName || `采场${item.stopeId}`)
  const planData = chartData.value.map((item: TDataFillingStopeStats) => item.planSlurryVolume || 0)
  const actualData = chartData.value.map((item: TDataFillingStopeStats) => item.totalSlurryVolume || 0)

  const getMax = (arr: number[]) => {
    let max = Math.round(Math.max(...arr))
    if (max === 0) return 100
    return (Number(max.toString()[0]) + 1) * Math.pow(10, max.toString().length - 1)
  }
  const xMax = getMax([...planData, ...actualData])

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        const axisValue = params[0].axisValue;
        let result = axisValue + '<br/>';
        
        // 获取计划和实际值的索引
        const planIndex = params.findIndex((p: any) => p.seriesName === '计划充填量');
        const actualIndex = params.findIndex((p: any) => p.seriesName === '实际充填量');
        
        // 计算完成率
        let completionRate = '';
        if (planIndex !== -1 && actualIndex !== -1) {
          const planValue = params[planIndex].value;
          const actualValue = params[actualIndex].value;
          if (planValue > 0) {
            const rate = (actualValue / planValue * 100).toFixed(2);
            completionRate = `<div style="margin:5px 0 10px 0;color:#666;">
              完成率: <strong>${rate}%</strong>
            </div>`;
          }
        }
        
        // 添加完成率
        result += completionRate;
        
        // 添加计划和实际值
        params.forEach((item: any) => {
          const value = item.value;
          const seriesName = item.seriesName;
          const color = item.color;

          result += `<div style="display:flex;align-items:center;margin:5px 0;">
            <span style="display:inline-block;width:10px;height:10px;background:${color};border-radius:50%;margin-right:5px;"></span>
            ${seriesName}: <strong>${value}m³</strong>
          </div>`;
        });
        return result;
      }
    },
    legend: {
      data: ['计划充填量', '实际充填量'],
      bottom: 0,
    },
    grid: {
      left: '15%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'value',
        name: '充填量(m³)',
        min: 0,
        max: xMax,
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#409EFF',
          },
        },
        nameTextStyle: {
          color: '#409EFF',
        },
      },
    ],
    yAxis: [
      {
        type: 'category',
        data: stopeNames,
        axisLabel: {
          interval: 0,
          formatter: (value: string) => {
            // 如果采场名称过长，进行截断
            return value.length > 8 ? value.substring(0, 8) + '...' : value
          },
        },
      },
    ],
    series: [
      {
        name: '计划充填量',
        type: 'bar',
        barWidth: '30%',
        data: planData,
        itemStyle: {
          color: '#5B9BD580',
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}',
          color: '#333',
          fontSize: 11,
          fontWeight: 'normal',
        },
      },
      {
        name: '实际充填量',
        type: 'bar',
        barWidth: '30%',
        data: actualData,
        itemStyle: {
          color: '#5B9BD5',
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}',
          color: '#333',
          fontSize: 11,
          fontWeight: 'normal',
        },
      },
    ],
  }
})
</script>
